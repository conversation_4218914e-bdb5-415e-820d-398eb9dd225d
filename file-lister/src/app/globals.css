@import "tailwindcss";

/* CSS Custom Properties for Dynamic Theming */
:root {
  /* Default Light Theme Colors */
  --color-background: rgb(255, 255, 255);
  --color-foreground: rgb(15, 23, 42);
  --color-card: rgb(255, 255, 255);
  --color-card-foreground: rgb(15, 23, 42);
  --color-primary: rgb(59, 130, 246);
  --color-primary-foreground: rgb(248, 250, 252);
  --color-secondary: rgb(241, 245, 249);
  --color-secondary-foreground: rgb(15, 23, 42);
  --color-muted: rgb(241, 245, 249);
  --color-muted-foreground: rgb(100, 116, 139);
  --color-accent: rgb(241, 245, 249);
  --color-accent-foreground: rgb(15, 23, 42);
  --color-destructive: rgb(239, 68, 68);
  --color-destructive-foreground: rgb(248, 250, 252);
  --color-border: rgb(226, 232, 240);
  --color-input: rgb(226, 232, 240);
  --color-ring: rgb(59, 130, 246);

  /* Default Font Family */
  --font-family: var(--font-inter);
}

/* Base Styles with Theme Variables */
body {
  background-color: var(--color-background);
  color: var(--color-foreground);
  font-family: var(--font-family);
  transition: background-color 0.3s ease, color 0.3s ease, font-family 0.3s ease;
}

/* Ensure all elements inherit the font family from body */
* {
  font-family: inherit;
}

/* Theme-aware component styles */
.theme-card {
  background-color: var(--color-card);
  color: var(--color-card-foreground);
  border-color: var(--color-border);
}

.theme-button-primary {
  background-color: var(--color-primary);
  color: var(--color-primary-foreground);
  border-color: var(--color-primary);
}

.theme-button-primary:hover {
  opacity: 0.9;
}

.theme-button-secondary {
  background-color: var(--color-secondary);
  color: var(--color-secondary-foreground);
  border-color: var(--color-border);
}

.theme-button-secondary:hover {
  background-color: var(--color-muted);
}

.theme-input {
  background-color: var(--color-background);
  color: var(--color-foreground);
  border-color: var(--color-border);
}

.theme-input:focus {
  border-color: var(--color-ring);
  outline: 2px solid var(--color-ring);
  outline-offset: 2px;
}

/* File type color overrides for better theming */
.file-type-video { color: var(--color-destructive); }
.file-type-audio { color: var(--color-secondary); }
.file-type-image { color: var(--color-accent); }
.file-type-document { color: var(--color-primary); }
.file-type-code { color: var(--color-ring); }
.file-type-archive { color: var(--color-muted-foreground); }
.file-type-directory { color: var(--color-primary); }

/* Smooth transitions for theme changes */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, font-family 0.3s ease;
}

/* Custom scrollbar theming */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-muted);
}

::-webkit-scrollbar-thumb {
  background: var(--color-muted-foreground);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-foreground);
}

/* Optimized popup animations with hardware acceleration */
@keyframes slideInFromRight {
  from {
    transform: translate3d(100%, 0, 0);
    opacity: 0;
  }
  to {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}

@keyframes slideOutToRight {
  from {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
  to {
    transform: translate3d(100%, 0, 0);
    opacity: 0;
  }
}

.popup-enter {
  animation: slideInFromRight 0.2s ease-out;
  will-change: transform, opacity;
  transform: translate3d(0, 0, 0); /* Force hardware acceleration */
}

.popup-exit {
  animation: slideOutToRight 0.2s ease-in;
  will-change: transform, opacity;
  transform: translate3d(0, 0, 0); /* Force hardware acceleration */
}

/* Performance optimizations for theme customizer */
.theme-customizer {
  contain: layout style paint;
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
}
