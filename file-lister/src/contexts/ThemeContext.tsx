'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { IconLibrary, FontFamily, ColorTheme, defaultTheme, colorThemes, fonts } from '@/lib/themes';
import { loadFont, getFontFamily, preloadEssentialFonts } from '@/lib/fontLoader';

interface ThemeContextType {
  iconLibrary: IconLibrary;
  font: FontFamily;
  colorTheme: ColorTheme;
  setIconLibrary: (library: IconLibrary) => void;
  setFont: (font: FontFamily) => void;
  setColorTheme: (theme: ColorTheme) => void;
  resetToDefaults: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

interface ThemeProviderProps {
  children: React.ReactNode;
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const [iconLibrary, setIconLibraryState] = useState<IconLibrary>(defaultTheme.iconLibrary);
  const [font, setFontState] = useState<FontFamily>(defaultTheme.font);
  const [colorTheme, setColorThemeState] = useState<ColorTheme>(defaultTheme.colorTheme);

  // Load theme from localStorage on mount and initialize font system
  useEffect(() => {
    // Initialize font loading system
    preloadEssentialFonts();

    const savedTheme = localStorage.getItem('file-lister-theme');
    if (savedTheme) {
      try {
        const parsed = JSON.parse(savedTheme);
        setIconLibraryState(parsed.iconLibrary || defaultTheme.iconLibrary);
        setFontState(parsed.font || defaultTheme.font);
        setColorThemeState(parsed.colorTheme || defaultTheme.colorTheme);
      } catch (error) {
        console.error('Failed to parse saved theme:', error);
      }
    }
  }, []);

  // Apply theme changes to document (optimized for performance)
  useEffect(() => {
    const theme = colorThemes[colorTheme];

    // Use requestAnimationFrame to avoid blocking the main thread
    requestAnimationFrame(() => {
      const root = document.documentElement;

      // Batch DOM updates for better performance
      const colorUpdates: [string, string][] = Object.entries(theme.colors);

      // Apply all color properties in a single batch
      colorUpdates.forEach(([key, value]) => {
        root.style.setProperty(`--color-${key}`, value);
      });

      // Apply theme class to body (optimized)
      const currentClasses = document.body.className.split(' ');
      const filteredClasses = currentClasses.filter(cls => !cls.startsWith('theme-'));
      document.body.className = [...filteredClasses, `theme-${colorTheme}`].join(' ');
    });

    // Debounce localStorage writes to avoid excessive I/O
    const saveTimeout = setTimeout(() => {
      localStorage.setItem('file-lister-theme', JSON.stringify({
        iconLibrary,
        font,
        colorTheme,
      }));
    }, 100);

    return () => clearTimeout(saveTimeout);
  }, [iconLibrary, font, colorTheme]);

  // Separate effect for font changes (non-blocking)
  useEffect(() => {
    // Use requestAnimationFrame for non-blocking font updates
    requestAnimationFrame(() => {
      const fontFamily = getFontFamily(font);
      const root = document.documentElement;

      // Apply font instantly (no async loading needed)
      root.style.setProperty('--font-family', fontFamily);
      document.body.style.fontFamily = fontFamily;
    });
  }, [font]);

  const setIconLibrary = (library: IconLibrary) => {
    setIconLibraryState(library);
  };

  const setFont = (newFont: FontFamily) => {
    setFontState(newFont);
  };

  const setColorTheme = (theme: ColorTheme) => {
    setColorThemeState(theme);
  };

  const resetToDefaults = () => {
    setIconLibraryState(defaultTheme.iconLibrary);
    setFontState(defaultTheme.font);
    setColorThemeState(defaultTheme.colorTheme);
  };

  return (
    <ThemeContext.Provider
      value={{
        iconLibrary,
        font,
        colorTheme,
        setIconLibrary,
        setFont,
        setColorTheme,
        resetToDefaults,
      }}
    >
      {children}
    </ThemeContext.Provider>
  );
}
