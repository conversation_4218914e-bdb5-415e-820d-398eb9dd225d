'use client';

import React, { useState } from 'react';
import { ThemePreset } from '@/lib/themes';
import { Icon } from '@/components/Icon';
import { colorThemes, fonts, iconLibraries } from '@/lib/themes';

interface PresetCardProps {
  preset: ThemePreset;
  isActive: boolean;
  onLoad: (presetId: string) => void;
  onRename?: (presetId: string, newName: string) => void;
  onDuplicate?: (presetId: string, newName: string) => void;
  onDelete?: (presetId: string) => void;
}

export const PresetCard = React.memo(function PresetCard({
  preset,
  isActive,
  onLoad,
  onRename,
  onDuplicate,
  onDelete,
}: PresetCardProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editName, setEditName] = useState(preset.name);
  const [showActions, setShowActions] = useState(false);

  const theme = colorThemes[preset.colorTheme];
  const fontConfig = fonts[preset.font];
  const iconLibConfig = iconLibraries[preset.iconLibrary];

  const handleRename = () => {
    if (editName.trim() && editName.trim() !== preset.name && onRename) {
      try {
        onRename(preset.id, editName.trim());
        setIsEditing(false);
      } catch (error) {
        console.error('Failed to rename preset:', error);
        // Reset to original name on error
        setEditName(preset.name);
      }
    } else {
      setIsEditing(false);
      setEditName(preset.name);
    }
  };

  const handleDuplicate = () => {
    if (onDuplicate) {
      const newName = `${preset.name} Copy`;
      try {
        onDuplicate(preset.id, newName);
      } catch (error) {
        console.error('Failed to duplicate preset:', error);
      }
    }
  };

  const handleDelete = () => {
    if (onDelete && !preset.isDefault) {
      if (confirm(`Are you sure you want to delete "${preset.name}"?`)) {
        try {
          onDelete(preset.id);
        } catch (error) {
          console.error('Failed to delete preset:', error);
        }
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleRename();
    } else if (e.key === 'Escape') {
      setIsEditing(false);
      setEditName(preset.name);
    }
  };

  return (
    <div
      className="relative p-4 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-md group"
      style={{
        borderColor: isActive ? 'var(--color-primary)' : 'var(--color-border)',
        backgroundColor: isActive ? 'var(--color-accent)' : 'var(--color-card)',
        color: isActive ? 'var(--color-accent-foreground)' : 'var(--color-card-foreground)',
      }}
      onClick={() => !isEditing && onLoad(preset.id)}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      {/* Default preset indicator */}
      {preset.isDefault && (
        <div
          className="absolute top-2 right-2 px-2 py-1 text-xs rounded"
          style={{
            backgroundColor: 'var(--color-primary)',
            color: 'var(--color-primary-foreground)',
          }}
        >
          Default
        </div>
      )}

      {/* Action buttons */}
      {showActions && !preset.isDefault && (
        <div className="absolute top-2 right-2 flex space-x-1">
          {onRename && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                setIsEditing(true);
              }}
              className="p-1 rounded hover:bg-opacity-20"
              style={{ backgroundColor: 'var(--color-muted)' }}
              title="Rename"
            >
              <Icon name="type" size={12} />
            </button>
          )}
          {onDuplicate && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleDuplicate();
              }}
              className="p-1 rounded hover:bg-opacity-20"
              style={{ backgroundColor: 'var(--color-muted)' }}
              title="Duplicate"
            >
              <Icon name="file" size={12} />
            </button>
          )}
          {onDelete && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleDelete();
              }}
              className="p-1 rounded hover:bg-opacity-20"
              style={{ backgroundColor: 'var(--color-destructive)' }}
              title="Delete"
            >
              <Icon name="archive" size={12} />
            </button>
          )}
        </div>
      )}

      {/* Main content - horizontal layout */}
      <div className="flex items-center justify-between">
        {/* Left side - Preset name and basic info */}
        <div className="flex-1 min-w-0">
          {isEditing ? (
            <input
              type="text"
              value={editName}
              onChange={(e) => setEditName(e.target.value)}
              onBlur={handleRename}
              onKeyDown={handleKeyDown}
              onClick={(e) => e.stopPropagation()}
              className="w-full bg-transparent border-b border-current outline-none font-medium text-base"
              style={{ color: 'inherit' }}
              autoFocus
            />
          ) : (
            <h3 className="font-medium text-base truncate mb-1" style={{ color: 'inherit' }}>
              {preset.name}
            </h3>
          )}

          {/* Quick preview info */}
          <div className="flex items-center space-x-4 text-xs opacity-75">
            <div className="flex items-center space-x-1">
              <Icon name="folder" size={12} library={preset.iconLibrary} />
              <span>{iconLibConfig.name}</span>
            </div>
            <div
              style={{
                fontFamily: `var(${fontConfig.variable}), ${fontConfig.name}, ${fontConfig.category === 'mono' ? 'monospace' : 'sans-serif'}`,
              }}
            >
              {fontConfig.name}
            </div>
            <div className="flex items-center space-x-1">
              <div
                className="w-2 h-2 rounded"
                style={{ backgroundColor: theme.colors.primary }}
              />
              <span>{theme.name}</span>
            </div>
          </div>
        </div>

        {/* Right side - Visual preview */}
        <div className="flex items-center space-x-4 ml-4">
          {/* Icon samples */}
          <div className="flex items-center space-x-1">
            <Icon name="folder" size={16} library={preset.iconLibrary} />
            <Icon name="file" size={16} library={preset.iconLibrary} />
            <Icon name="file-image" size={16} library={preset.iconLibrary} />
          </div>

          {/* Color swatches */}
          <div className="flex items-center space-x-1">
            <div
              className="w-4 h-4 rounded border"
              style={{
                backgroundColor: theme.colors.background,
                borderColor: 'var(--color-border)',
              }}
            />
            <div
              className="w-4 h-4 rounded"
              style={{ backgroundColor: theme.colors.primary }}
            />
            <div
              className="w-4 h-4 rounded"
              style={{ backgroundColor: theme.colors.accent }}
            />
          </div>

          {/* Sample text preview */}
          <div
            className="text-xs px-3 py-2 rounded border min-w-0"
            style={{
              backgroundColor: theme.colors.background,
              color: theme.colors.foreground,
              borderColor: theme.colors.border,
              fontFamily: `var(${fontConfig.variable}), ${fontConfig.name}, ${fontConfig.category === 'mono' ? 'monospace' : 'sans-serif'}`,
            }}
          >
            <div className="flex items-center space-x-1 whitespace-nowrap">
              <Icon name="file" size={10} library={preset.iconLibrary} />
              <span>Sample</span>
            </div>
          </div>
        </div>
      </div>

      {/* Active indicator */}
      {isActive && (
        <div
          className="absolute bottom-2 right-2 w-2 h-2 rounded-full"
          style={{ backgroundColor: 'var(--color-primary)' }}
        />
      )}
    </div>
  );
});
