'use client';

import React, { useState, useMemo } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { PresetCard } from '@/components/PresetCard';
import { Icon } from '@/components/Icon';

export const PresetManager = React.memo(function PresetManager() {
  const {
    presets,
    currentPresetId,
    savePreset,
    loadPreset,
    deletePreset,
    renamePreset,
    duplicatePreset,
  } = useTheme();

  const [searchQuery, setSearchQuery] = useState('');
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [newPresetName, setNewPresetName] = useState('');
  const [saveError, setSaveError] = useState('');

  // Filter presets based on search query
  const filteredPresets = useMemo(() => {
    if (!searchQuery.trim()) return presets;
    
    const query = searchQuery.toLowerCase();
    return presets.filter(preset =>
      preset.name.toLowerCase().includes(query) ||
      preset.iconLibrary.toLowerCase().includes(query) ||
      preset.font.toLowerCase().includes(query) ||
      preset.colorTheme.toLowerCase().includes(query)
    );
  }, [presets, searchQuery]);

  // Separate default and custom presets
  const defaultPresets = filteredPresets.filter(p => p.isDefault);
  const customPresets = filteredPresets.filter(p => !p.isDefault);

  const handleSavePreset = async () => {
    if (!newPresetName.trim()) {
      setSaveError('Please enter a preset name');
      return;
    }

    try {
      await savePreset(newPresetName.trim());
      setShowSaveDialog(false);
      setNewPresetName('');
      setSaveError('');
    } catch (error) {
      setSaveError(error instanceof Error ? error.message : 'Failed to save preset');
    }
  };

  const handleSaveDialogKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSavePreset();
    } else if (e.key === 'Escape') {
      setShowSaveDialog(false);
      setNewPresetName('');
      setSaveError('');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header with save button and search */}
      <div className="flex flex-col space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-base font-medium" style={{ color: 'var(--color-foreground)' }}>
            Theme Presets
          </h3>
          <button
            onClick={() => setShowSaveDialog(true)}
            className="flex items-center space-x-2 px-3 py-2 rounded border transition-colors"
            style={{
              borderColor: 'var(--color-primary)',
              backgroundColor: 'var(--color-primary)',
              color: 'var(--color-primary-foreground)',
            }}
          >
            <Icon name="settings" size={16} />
            <span className="text-sm">Save Current</span>
          </button>
        </div>

        {/* Search bar */}
        {presets.length > 6 && (
          <div className="relative">
            <input
              type="text"
              placeholder="Search presets..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-3 py-2 rounded border outline-none transition-colors"
              style={{
                borderColor: 'var(--color-border)',
                backgroundColor: 'var(--color-background)',
                color: 'var(--color-foreground)',
              }}
            />
            <Icon
              name="settings"
              size={16}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 opacity-50"
            />
          </div>
        )}
      </div>

      {/* Save preset dialog */}
      {showSaveDialog && (
        <div
          className="p-4 rounded border"
          style={{
            borderColor: 'var(--color-border)',
            backgroundColor: 'var(--color-card)',
          }}
        >
          <h4 className="font-medium mb-3" style={{ color: 'var(--color-card-foreground)' }}>
            Save Current Theme as Preset
          </h4>
          <div className="space-y-3">
            <input
              type="text"
              placeholder="Enter preset name..."
              value={newPresetName}
              onChange={(e) => setNewPresetName(e.target.value)}
              onKeyDown={handleSaveDialogKeyDown}
              className="w-full px-3 py-2 rounded border outline-none"
              style={{
                borderColor: saveError ? 'var(--color-destructive)' : 'var(--color-border)',
                backgroundColor: 'var(--color-background)',
                color: 'var(--color-foreground)',
              }}
              autoFocus
            />
            {saveError && (
              <p className="text-sm" style={{ color: 'var(--color-destructive)' }}>
                {saveError}
              </p>
            )}
            <div className="flex space-x-2">
              <button
                onClick={handleSavePreset}
                className="px-3 py-2 rounded text-sm transition-colors"
                style={{
                  backgroundColor: 'var(--color-primary)',
                  color: 'var(--color-primary-foreground)',
                }}
              >
                Save
              </button>
              <button
                onClick={() => {
                  setShowSaveDialog(false);
                  setNewPresetName('');
                  setSaveError('');
                }}
                className="px-3 py-2 rounded text-sm transition-colors"
                style={{
                  backgroundColor: 'var(--color-secondary)',
                  color: 'var(--color-secondary-foreground)',
                }}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Default presets */}
      {defaultPresets.length > 0 && (
        <div>
          <h4 className="text-sm font-medium mb-3 opacity-75" style={{ color: 'var(--color-foreground)' }}>
            Default Presets
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {defaultPresets.map((preset) => (
              <PresetCard
                key={preset.id}
                preset={preset}
                isActive={currentPresetId === preset.id}
                onLoad={loadPreset}
              />
            ))}
          </div>
        </div>
      )}

      {/* Custom presets */}
      {customPresets.length > 0 && (
        <div>
          <h4 className="text-sm font-medium mb-3 opacity-75" style={{ color: 'var(--color-foreground)' }}>
            Custom Presets
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {customPresets.map((preset) => (
              <PresetCard
                key={preset.id}
                preset={preset}
                isActive={currentPresetId === preset.id}
                onLoad={loadPreset}
                onRename={renamePreset}
                onDuplicate={duplicatePreset}
                onDelete={deletePreset}
              />
            ))}
          </div>
        </div>
      )}

      {/* Empty state for custom presets */}
      {customPresets.length === 0 && !searchQuery && (
        <div className="text-center py-8">
          <div className="mb-4">
            <Icon name="palette" size={48} className="mx-auto opacity-50" />
          </div>
          <h4 className="font-medium mb-2" style={{ color: 'var(--color-foreground)' }}>
            No Custom Presets Yet
          </h4>
          <p className="text-sm opacity-75 mb-4" style={{ color: 'var(--color-foreground)' }}>
            Create your first custom preset by clicking "Save Current" above.
          </p>
        </div>
      )}

      {/* No search results */}
      {filteredPresets.length === 0 && searchQuery && (
        <div className="text-center py-8">
          <div className="mb-4">
            <Icon name="settings" size={48} className="mx-auto opacity-50" />
          </div>
          <h4 className="font-medium mb-2" style={{ color: 'var(--color-foreground)' }}>
            No Presets Found
          </h4>
          <p className="text-sm opacity-75" style={{ color: 'var(--color-foreground)' }}>
            No presets match your search for "{searchQuery}".
          </p>
        </div>
      )}

      {/* Preset count */}
      <div className="text-xs opacity-50 text-center" style={{ color: 'var(--color-foreground)' }}>
        {presets.length} preset{presets.length !== 1 ? 's' : ''} total
        {searchQuery && ` • ${filteredPresets.length} shown`}
      </div>
    </div>
  );
});
