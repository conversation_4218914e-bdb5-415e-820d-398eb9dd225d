// Optimized font loading utility
// Uses Next.js preloaded fonts for instant switching without CORS issues

interface FontConfig {
  family: string;
  variable: string;
  category: 'sans' | 'mono';
}

// Font configurations matching Next.js font variables
const fontConfigs: Record<string, FontConfig> = {
  'inter': {
    family: 'Inter',
    variable: '--font-inter',
    category: 'sans'
  },
  'roboto': {
    family: 'Roboto',
    variable: '--font-roboto',
    category: 'sans'
  },
  'poppins': {
    family: 'Poppins',
    variable: '--font-poppins',
    category: 'sans'
  },
  'open-sans': {
    family: 'Open Sans',
    variable: '--font-open-sans',
    category: 'sans'
  },
  'lato': {
    family: 'Lato',
    variable: '--font-lato',
    category: 'sans'
  },
  'montserrat': {
    family: 'Montserrat',
    variable: '--font-montserrat',
    category: 'sans'
  },
  'source-sans': {
    family: 'Source Sans 3',
    variable: '--font-source-sans',
    category: 'sans'
  },
  'nunito': {
    family: 'Nunito',
    variable: '--font-nunito',
    category: 'sans'
  },

  'jetbrains-mono': {
    family: 'JetBrains Mono',
    variable: '--font-jetbrains-mono',
    category: 'mono'
  },
  'fira-code': {
    family: 'Fira Code',
    variable: '--font-fira-code',
    category: 'mono'
  },
  'source-code-pro': {
    family: 'Source Code Pro',
    variable: '--font-source-code-pro',
    category: 'mono'
  }
};

/**
 * Instantly apply a preloaded font (no loading required)
 * All fonts are preloaded via Next.js in layout.tsx
 */
export async function loadFont(fontKey: string): Promise<void> {
  const config = fontConfigs[fontKey];
  if (!config) {
    console.warn(`Font configuration not found for: ${fontKey}`);
    return;
  }

  // All fonts are already loaded via Next.js, so this is instant
  console.log(`Font ready: ${config.family} (preloaded via Next.js)`);
  return Promise.resolve();
}

/**
 * Get the CSS font family string using Next.js CSS variables
 */
export function getFontFamily(fontKey: string): string {
  const config = fontConfigs[fontKey];
  if (!config) {
    return 'var(--font-inter), Inter, sans-serif'; // Default fallback
  }

  // Use CSS variable with appropriate fallback
  const fallback = config.category === 'mono' ? 'monospace' : 'sans-serif';

  return `var(${config.variable}), "${config.family}", ${fallback}`;
}

/**
 * Check if a font is available (all fonts are preloaded via Next.js)
 */
export function isFontLoaded(fontKey: string): boolean {
  return fontConfigs[fontKey] !== undefined;
}

/**
 * Initialize font system (all fonts are preloaded via Next.js)
 */
export function preloadEssentialFonts(): void {
  // All fonts are preloaded via Next.js layout, no action needed
  console.log('Font system initialized - all fonts preloaded via Next.js');
}

/**
 * Get all available font configurations
 */
export function getAllFonts(): Record<string, FontConfig> {
  return fontConfigs;
}
