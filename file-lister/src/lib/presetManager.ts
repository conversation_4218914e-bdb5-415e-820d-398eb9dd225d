// Theme Preset Manager
// Handles CRUD operations for theme presets with localStorage persistence

import { ThemePreset, PresetStorage, IconLibrary, FontFamily, ColorTheme } from './themes';

const PRESET_STORAGE_KEY = 'file-lister-presets';
const THEME_STORAGE_KEY = 'file-lister-theme';

// Generate unique ID for presets
function generatePresetId(): string {
  return `preset_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Default preset definitions
export const defaultPresets: ThemePreset[] = [
  {
    id: 'modern-dark',
    name: 'Modern Dark',
    iconLibrary: 'lucide',
    font: 'inter',
    colorTheme: 'dark',
    isDefault: true,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: 'professional-light',
    name: 'Professional Light',
    iconLibrary: 'heroicons',
    font: 'roboto',
    colorTheme: 'light',
    isDefault: true,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: 'developer-setup',
    name: 'Developer Setup',
    iconLibrary: 'tabler',
    font: 'jetbrains-mono',
    colorTheme: 'one-dark',
    isDefault: true,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: 'minimal-clean',
    name: 'Minimal Clean',
    iconLibrary: 'emoji',
    font: 'open-sans',
    colorTheme: 'light',
    isDefault: true,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: 'creative-vibrant',
    name: 'Creative Vibrant',
    iconLibrary: 'react-icons',
    font: 'poppins',
    colorTheme: 'material',
    isDefault: true,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: 'classic-professional',
    name: 'Classic Professional',
    iconLibrary: 'heroicons-solid',
    font: 'source-sans',
    colorTheme: 'github',
    isDefault: true,
    createdAt: new Date('2024-01-01'),
  },
];

// Validate preset data structure
function validatePreset(preset: any): preset is ThemePreset {
  return (
    typeof preset === 'object' &&
    typeof preset.id === 'string' &&
    typeof preset.name === 'string' &&
    typeof preset.iconLibrary === 'string' &&
    typeof preset.font === 'string' &&
    typeof preset.colorTheme === 'string' &&
    typeof preset.isDefault === 'boolean' &&
    preset.createdAt instanceof Date || typeof preset.createdAt === 'string'
  );
}

// Get all presets from localStorage with fallback to defaults
export function getPresets(): ThemePreset[] {
  try {
    const stored = localStorage.getItem(PRESET_STORAGE_KEY);
    if (!stored) {
      // First time - initialize with defaults
      const storage: PresetStorage = {
        presets: [...defaultPresets],
      };
      localStorage.setItem(PRESET_STORAGE_KEY, JSON.stringify(storage));
      return defaultPresets;
    }

    const parsed: PresetStorage = JSON.parse(stored);
    if (!Array.isArray(parsed.presets)) {
      throw new Error('Invalid presets format');
    }

    // Validate and convert dates
    const validPresets = parsed.presets
      .filter(validatePreset)
      .map(preset => ({
        ...preset,
        createdAt: new Date(preset.createdAt),
      }));

    // Ensure we always have default presets
    const hasDefaults = validPresets.some(p => p.isDefault);
    if (!hasDefaults) {
      return [...defaultPresets, ...validPresets.filter(p => !p.isDefault)];
    }

    return validPresets;
  } catch (error) {
    console.error('Failed to load presets, using defaults:', error);
    return [...defaultPresets];
  }
}

// Save presets to localStorage
function savePresets(presets: ThemePreset[]): void {
  try {
    const storage: PresetStorage = {
      presets,
      lastUsedPresetId: getCurrentPresetId(),
    };
    localStorage.setItem(PRESET_STORAGE_KEY, JSON.stringify(storage));
  } catch (error) {
    console.error('Failed to save presets:', error);
    throw new Error('Unable to save presets. Storage may be full.');
  }
}

// Get current preset ID from storage
function getCurrentPresetId(): string | undefined {
  try {
    const stored = localStorage.getItem(PRESET_STORAGE_KEY);
    if (stored) {
      const parsed: PresetStorage = JSON.parse(stored);
      return parsed.lastUsedPresetId;
    }
  } catch (error) {
    console.error('Failed to get current preset ID:', error);
  }
  return undefined;
}

// Set current preset ID
export function setCurrentPresetId(presetId: string | undefined): void {
  try {
    const presets = getPresets();
    const storage: PresetStorage = {
      presets,
      lastUsedPresetId: presetId,
    };
    localStorage.setItem(PRESET_STORAGE_KEY, JSON.stringify(storage));
  } catch (error) {
    console.error('Failed to set current preset ID:', error);
  }
}

// Create a new preset
export function createPreset(
  name: string,
  iconLibrary: IconLibrary,
  font: FontFamily,
  colorTheme: ColorTheme
): ThemePreset {
  const preset: ThemePreset = {
    id: generatePresetId(),
    name: name.trim(),
    iconLibrary,
    font,
    colorTheme,
    isDefault: false,
    createdAt: new Date(),
  };

  const presets = getPresets();
  
  // Check for duplicate names
  const existingNames = presets.map(p => p.name.toLowerCase());
  if (existingNames.includes(preset.name.toLowerCase())) {
    throw new Error(`A preset named "${preset.name}" already exists`);
  }

  presets.push(preset);
  savePresets(presets);
  
  return preset;
}

// Update an existing preset
export function updatePreset(presetId: string, updates: Partial<Omit<ThemePreset, 'id' | 'isDefault' | 'createdAt'>>): ThemePreset {
  const presets = getPresets();
  const index = presets.findIndex(p => p.id === presetId);
  
  if (index === -1) {
    throw new Error(`Preset with ID "${presetId}" not found`);
  }

  const preset = presets[index];
  
  if (preset.isDefault) {
    throw new Error('Cannot modify default presets');
  }

  // Check for duplicate names if name is being updated
  if (updates.name && updates.name !== preset.name) {
    const existingNames = presets
      .filter(p => p.id !== presetId)
      .map(p => p.name.toLowerCase());
    
    if (existingNames.includes(updates.name.toLowerCase())) {
      throw new Error(`A preset named "${updates.name}" already exists`);
    }
  }

  const updatedPreset = {
    ...preset,
    ...updates,
    name: updates.name?.trim() || preset.name,
  };

  presets[index] = updatedPreset;
  savePresets(presets);
  
  return updatedPreset;
}

// Delete a preset
export function deletePreset(presetId: string): void {
  const presets = getPresets();
  const preset = presets.find(p => p.id === presetId);
  
  if (!preset) {
    throw new Error(`Preset with ID "${presetId}" not found`);
  }

  if (preset.isDefault) {
    throw new Error('Cannot delete default presets');
  }

  // Prevent deletion of last remaining preset
  const userPresets = presets.filter(p => !p.isDefault);
  if (userPresets.length <= 1) {
    throw new Error('Cannot delete the last remaining custom preset');
  }

  const filteredPresets = presets.filter(p => p.id !== presetId);
  savePresets(filteredPresets);

  // Clear current preset if it was deleted
  const currentId = getCurrentPresetId();
  if (currentId === presetId) {
    setCurrentPresetId(undefined);
  }
}

// Duplicate a preset
export function duplicatePreset(presetId: string, newName: string): ThemePreset {
  const presets = getPresets();
  const originalPreset = presets.find(p => p.id === presetId);
  
  if (!originalPreset) {
    throw new Error(`Preset with ID "${presetId}" not found`);
  }

  return createPreset(
    newName,
    originalPreset.iconLibrary,
    originalPreset.font,
    originalPreset.colorTheme
  );
}

// Get a specific preset by ID
export function getPresetById(presetId: string): ThemePreset | undefined {
  const presets = getPresets();
  return presets.find(p => p.id === presetId);
}

// Migration: Create preset from existing theme settings
export function migrateCurrentTheme(): ThemePreset | null {
  try {
    const stored = localStorage.getItem(THEME_STORAGE_KEY);
    if (!stored) return null;

    const currentTheme = JSON.parse(stored);
    if (!currentTheme.iconLibrary || !currentTheme.font || !currentTheme.colorTheme) {
      return null;
    }

    // Check if this combination already exists
    const presets = getPresets();
    const existing = presets.find(p => 
      p.iconLibrary === currentTheme.iconLibrary &&
      p.font === currentTheme.font &&
      p.colorTheme === currentTheme.colorTheme
    );

    if (existing) {
      setCurrentPresetId(existing.id);
      return existing;
    }

    // Create new preset from current theme
    const migratedPreset = createPreset(
      'My Current Theme',
      currentTheme.iconLibrary,
      currentTheme.font,
      currentTheme.colorTheme
    );

    setCurrentPresetId(migratedPreset.id);
    return migratedPreset;
  } catch (error) {
    console.error('Failed to migrate current theme:', error);
    return null;
  }
}
